<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .video-item {
            transition: transform 0.2s ease-in-out;
        }
        .video-item:hover {
            transform: scale(1.02);
        }
        .modal {
            backdrop-filter: blur(5px);
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .tab-scroll::-webkit-scrollbar {
            display: none;
        }
        .tab-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <!-- 头部分类选择 -->
    <div class="bg-white shadow-sm sticky top-0 z-10">
        <div class="container mx-auto px-4 py-3">
            <h1 class="text-xl font-bold text-gray-800 mb-3">视频列表</h1>
            <div class="flex overflow-x-auto space-x-2 tab-scroll" id="categoryTabs">
                <!-- 分类标签将通过JS动态生成 -->
            </div>
        </div>
    </div>

    <!-- 视频列表容器 -->
    <div class="container mx-auto px-4 py-4">
        <div id="videoList" class="space-y-4">
            <!-- 视频列表将通过JS动态生成 -->
        </div>

        <!-- 加载更多按钮 -->
        <div class="text-center mt-6">
            <button id="loadMoreBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                加载更多
            </button>
            <div id="loadingIndicator" class="hidden mt-4">
                <div class="inline-block w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full loading"></div>
                <span class="ml-2 text-gray-600">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 视频播放弹窗 -->
    <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-50 modal hidden z-50 flex items-center justify-center">
        <div class="bg-white rounded-lg p-4 max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 id="modalTitle" class="text-lg font-bold text-gray-800"></h3>
                <button id="closeModal" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
            </div>
            <div class="relative">
                <video id="videoPlayer" class="w-full h-auto max-h-[70vh]" controls>
                    您的浏览器不支持视频播放。
                </video>
            </div>
        </div>
    </div>

    <script>
        // 分类配置
        const types = [
            "next=watch",
            "category=hot",
            "category=top",
            "category=nonpaid",
            "category=long",
            "category=longer",
            "category=tf",
            "category=rf",
            "category=hd",
            "category=top&m=-1",
            "category=md",
            "category=mf",
        ];

        const typeNames = [
            "最新", "热门", "排行", "免费", "长视频", "超长", "TF", "RF", "高清", "排行-1", "MD", "MF"
        ];

        // 全局变量
        let currentType = 0;
        let currentPage = 1;
        let isLoading = false;
        let videoList = [];

        // Base64解码函数（不使用atob）
        function base64Decode(str) {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
            let result = '';
            let i = 0;

            str = str.replace(/[^A-Za-z0-9+/]/g, '');

            while (i < str.length) {
                const encoded1 = chars.indexOf(str.charAt(i++));
                const encoded2 = chars.indexOf(str.charAt(i++));
                const encoded3 = chars.indexOf(str.charAt(i++));
                const encoded4 = chars.indexOf(str.charAt(i++));

                const bitmap = (encoded1 << 18) | (encoded2 << 12) | (encoded3 << 6) | encoded4;

                result += String.fromCharCode((bitmap >> 16) & 255);
                if (encoded3 !== 64) result += String.fromCharCode((bitmap >> 8) & 255);
                if (encoded4 !== 64) result += String.fromCharCode(bitmap & 255);
            }

            return result;
        }

        // 初始化分类标签
        function initCategoryTabs() {
            const tabsContainer = document.getElementById('categoryTabs');
            typeNames.forEach((name, index) => {
                const tab = document.createElement('button');
                tab.className = `px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                    index === currentType
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`;
                tab.textContent = name;
                tab.onclick = () => switchCategory(index);
                tabsContainer.appendChild(tab);
            });
        }

        // 切换分类
        function switchCategory(typeIndex) {
            if (typeIndex === currentType || isLoading) return;

            currentType = typeIndex;
            currentPage = 1;
            videoList = [];

            // 更新标签样式
            const tabs = document.querySelectorAll('#categoryTabs button');
            tabs.forEach((tab, index) => {
                tab.className = `px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                    index === currentType
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`;
            });

            // 清空列表并加载新数据
            document.getElementById('videoList').innerHTML = '';
            loadVideos(true);
        }

        // 加载视频数据
        async function loadVideos(isRefresh = false) {
            if (isLoading) return;

            isLoading = true;
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadMoreBtn = document.getElementById('loadMoreBtn');

            loadingIndicator.classList.remove('hidden');
            loadMoreBtn.style.display = 'none';

            try {
                const response = await fetch(`http://127.0.0.1:3000/?type=${currentType}&page=${currentPage}`);
                const result = await response.json();

                if (result.error) {
                    throw new Error('获取数据失败');
                }

                // 解码数据
                const decodedData = base64Decode(result.data);
                const videos = JSON.parse(decodedData);

                if (isRefresh) {
                    videoList = videos;
                } else {
                    videoList = [...videoList, ...videos];
                }

                renderVideoList();
                currentPage++;

            } catch (error) {
                console.error('加载视频失败:', error);
                alert('加载视频失败，请稍后重试');
            } finally {
                isLoading = false;
                loadingIndicator.classList.add('hidden');
                loadMoreBtn.style.display = 'block';
            }
        }

        // 渲染视频列表
        function renderVideoList() {
            const container = document.getElementById('videoList');
            container.innerHTML = '';

            videoList.forEach((video, index) => {
                const videoItem = document.createElement('div');
                videoItem.className = 'bg-white rounded-lg shadow-md overflow-hidden video-item cursor-pointer';
                videoItem.onclick = () => playVideo(video);

                videoItem.innerHTML = `
                    <div class="flex">
                        <div class="w-48 h-32 flex-shrink-0">
                            <img src="${video.img}" alt="${video.title}"
                                 class="w-full h-full object-cover"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyIiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDE5MiAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxOTIiIGhlaWdodD0iMTI4IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04NCA2NEw5NiA3Mkg4NFY2NFoiIGZpbGw9IiM5Q0EzQUYiLz4KPC9zdmc+'" />
                        </div>
                        <div class="flex-1 p-4">
                            <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2">${video.title}</h3>
                            <p class="text-sm text-gray-600">${video.time}</p>
                        </div>
                    </div>
                `;

                container.appendChild(videoItem);
            });
        }

        // 播放视频
        async function playVideo(video) {
            const modal = document.getElementById('videoModal');
            const modalTitle = document.getElementById('modalTitle');
            const videoPlayer = document.getElementById('videoPlayer');

            modalTitle.textContent = video.title;
            modal.classList.remove('hidden');

            try {
                // 获取视频播放地址
                const response = await fetch(`http://127.0.0.1:3000/?id=${video.url}`);
                const result = await response.json();

                if (result.error) {
                    throw new Error('获取视频地址失败');
                }

                // 解码视频地址
                const decodedData = base64Decode(result.data);
                const videoData = JSON.parse(decodedData);

                videoPlayer.src = videoData.url;
                videoPlayer.load();

            } catch (error) {
                console.error('获取视频地址失败:', error);
                alert('获取视频地址失败，请稍后重试');
                closeVideoModal();
            }
        }

        // 关闭视频弹窗
        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const videoPlayer = document.getElementById('videoPlayer');

            modal.classList.add('hidden');
            videoPlayer.pause();
            videoPlayer.src = '';
        }

        // 上拉刷新功能
        let startY = 0;
        let isRefreshing = false;

        document.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchmove', (e) => {
            const currentY = e.touches[0].clientY;
            const diff = currentY - startY;

            if (diff > 100 && window.scrollY === 0 && !isRefreshing && !isLoading) {
                isRefreshing = true;
                refreshVideos();
            }
        });

        // 刷新视频列表
        async function refreshVideos() {
            currentPage = 1;
            await loadVideos(true);
            isRefreshing = false;
        }

        // 事件监听器
        document.getElementById('loadMoreBtn').addEventListener('click', () => {
            loadVideos();
        });

        document.getElementById('closeModal').addEventListener('click', closeVideoModal);

        document.getElementById('videoModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                closeVideoModal();
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeVideoModal();
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            initCategoryTabs();
            loadVideos(true);
        });
    </script>
</body>
</html>
