<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仿抖音视频播放页</title>
    <!-- Favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🎵</text></svg>">
    <!-- TailwindCSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body class="bg-black overflow-hidden">
    <!-- 主容器 -->
    <div class="relative w-full h-screen">
        <!-- 视频容器 -->
        <div id="video-container" class="relative w-full h-full">
            <!-- 视频播放器 -->
            <video
                id="main-video"
                class="w-full h-full object-cover"
                loop
                muted
                playsinline
                onclick="togglePlay()"
            >
                <!-- 视频源将通过JavaScript动态设置 -->
                您的浏览器不支持视频播放
            </video>

            <!-- 播放/暂停图标 -->
            <div id="play-pause-icon" class="absolute inset-0 flex items-center justify-center pointer-events-none opacity-0 transition-opacity duration-300">
                <i class="fas fa-play text-white text-6xl"></i>
            </div>

            <!-- 顶部导航 -->
            <div class="absolute top-0 left-0 right-0 z-20 p-4 bg-gradient-to-b from-black/50 to-transparent">
                <div class="flex justify-between items-center">
                    <i class="fas fa-arrow-left text-white text-xl"></i>
                    <div class="flex space-x-4">
                        <i class="fas fa-search text-white text-xl"></i>
                        <i class="fas fa-ellipsis-v text-white text-xl"></i>
                    </div>
                </div>
            </div>

            <!-- 右侧操作栏 -->
            <div class="absolute right-4 bottom-20 z-20 flex flex-col space-y-6">
                <!-- 播放列表按钮 -->
                <div class="flex flex-col items-center">
                    <button id="playlist-btn" class="w-12 h-12 flex items-center justify-center action-button" onclick="togglePlaylist()">
                        <i class="fas fa-list text-white text-2xl"></i>
                    </button>
                    <span class="text-white text-xs mt-1">列表</span>
                </div>

                <!-- 音乐图标 -->
                <div class="flex flex-col items-center">
                    <div class="w-12 h-12 bg-gray-800 rounded-full flex items-center justify-center rotating">
                        <i class="fas fa-music text-white text-lg"></i>
                    </div>
                </div>
            </div>

            <!-- 底部信息栏 -->
            <div class="absolute bottom-0 left-0 right-0 z-20 p-4 bg-gradient-to-t from-black/70 to-transparent">
                <div class="mb-4">
                    <!-- 视频标题 -->
                    <h3 id="video-title" class="text-white font-semibold text-lg mb-2">
                        视频标题
                    </h3>

                    <!-- 视频时间 -->
                    <div class="flex items-center">
                        <i class="fas fa-clock text-white text-xs mr-2"></i>
                        <span id="video-time" class="text-white text-xs">00:00</span>
                    </div>
                </div>

                <!-- 进度条 -->
                <div class="w-full bg-gray-600 rounded-full h-1">
                    <div id="progress-bar" class="bg-white h-1 rounded-full transition-all duration-100" style="width: 0%"></div>
                </div>
            </div>
        </div>

        <!-- 播放列表弹窗 -->
        <div id="playlist-modal" class="fixed inset-0 bg-black/50 z-50 hidden">
            <div class="absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-2/3 overflow-hidden">
                <div class="p-4 border-b">
                    <div class="flex justify-between items-center">
                        <span class="font-semibold">播放列表</span>
                        <button onclick="hidePlaylist()">
                            <i class="fas fa-times text-gray-500"></i>
                        </button>
                    </div>
                </div>
                <div id="playlist-content" class="p-4 space-y-3 max-h-96 overflow-y-auto custom-scrollbar">
                    <!-- 播放列表将在这里动态生成 -->
                    <div class="flex items-center justify-center py-8">
                        <div class="loading-spinner"></div>
                        <span class="ml-3 text-gray-500">加载中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
