// 全局变量
let isPlaying = false;
let currentVideo = 0;
let startY = 0;
let endY = 0;
let videos = []; // 从API获取的视频列表
let currentPage = 1;
let isLoading = false;

// API配置
const API_BASE_URL = '127.0.0.1:3000';

// Base64解码函数
function base64Decode(str) {
    try {
        // 使用现代方式解码base64
        const binaryString = window.atob(str);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        const decoder = new TextDecoder('utf-8');
        return decoder.decode(bytes);
    } catch (e) {
        console.error('Base64解码失败:', e);
        return null;
    }
}

// 获取视频列表
async function fetchVideos(page = 1, type = 1) {
    if (isLoading) return;

    isLoading = true;
    try {
        const response = await fetch(`http://${API_BASE_URL}/?type=${type}&page=${page}`);
        const result = await response.json();

        if (result.error) {
            console.error('API返回错误:', result.error);
            return [];
        }

        if (result.data) {
            const decodedData = base64Decode(result.data);
            if (decodedData) {
                const videoList = JSON.parse(decodedData);
                return videoList.map(video => ({
                    title: video.title,
                    id: video.url, // 这里是视频ID，不是真实URL
                    time: video.time,
                    img: video.img,
                    realUrl: null // 真实URL需要通过ID获取
                }));
            }
        }

        return [];
    } catch (error) {
        console.error('获取视频列表失败:', error);
        return [];
    } finally {
        isLoading = false;
    }
}

// 获取视频真实URL
async function fetchVideoUrl(id) {
    try {
        const response = await fetch(`http://${API_BASE_URL}/?id=${id}`);
        const result = await response.json();

        if (result.error) {
            console.error('获取视频URL失败:', result.error);
            return null;
        }

        if (result.data) {
            const decodedData = base64Decode(result.data);
            if (decodedData) {
                const urlData = JSON.parse(decodedData);
                return urlData.url;
            }
        }

        return null;
    } catch (error) {
        console.error('获取视频URL失败:', error);
        return null;
    }
}

// 初始化视频列表
async function initializeVideos() {
    const videoList = await fetchVideos(1, 1);
    if (videoList.length > 0) {
        videos = videoList;
        console.log('成功加载视频列表:', videos.length, '个视频');
    } else {
        console.log('未能加载视频列表，使用默认视频');
        // 如果API失败，使用默认视频
        videos = [{
            title: "默认视频",
            id: "default",
            time: "10:34",
            img: "https://picsum.photos/300/200?random=1",
            realUrl: "https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
        }];
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', async function() {
    const video = document.getElementById('main-video');

    // 初始化加载视频列表
    await initializeVideos();

    // 自动播放视频
    if (videos.length > 0) {
        loadVideo(0);
        video.play().then(() => {
            isPlaying = true;
            updatePlayPauseIcon();
        }).catch(e => {
            console.log('自动播放失败:', e);
            // 显示播放按钮提示用户手动播放
            const playIcon = document.getElementById('play-pause-icon');
            playIcon.classList.remove('opacity-0');
            playIcon.innerHTML = '<i class="fas fa-play text-white text-6xl"></i>';
        });
    }

    // 监听视频时间更新，更新进度条
    video.addEventListener('timeupdate', updateProgress);

    // 监听视频结束事件
    video.addEventListener('ended', function() {
        // 视频结束后重新播放
        video.currentTime = 0;
        video.play();
    });

    // 监听视频加载错误
    video.addEventListener('error', function(e) {
        console.error('视频加载失败:', e);
        // 可以在这里显示错误提示或加载下一个视频
    });

    // 监听视频可以播放时
    video.addEventListener('canplay', function() {
        console.log('视频可以播放');
    });

    // 添加触摸事件监听（用于滑动切换视频）
    document.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
    });

    document.addEventListener('touchend', function(e) {
        endY = e.changedTouches[0].clientY;
        handleSwipe();
    });

    // 键盘事件监听
    document.addEventListener('keydown', function(e) {
        switch(e.key) {
            case ' ':
                e.preventDefault();
                togglePlay();
                break;
            case 'ArrowUp':
                e.preventDefault();
                previousVideo();
                break;
            case 'ArrowDown':
                e.preventDefault();
                nextVideo();
                break;
        }
    });
});

// 切换播放/暂停
function togglePlay() {
    const video = document.getElementById('main-video');
    const icon = document.getElementById('play-pause-icon');

    if (isPlaying) {
        video.pause();
        isPlaying = false;
        icon.innerHTML = '<i class="fas fa-play text-white text-6xl"></i>';
        icon.classList.remove('opacity-0');
        setTimeout(() => {
            icon.classList.add('opacity-0');
        }, 1000);
    } else {
        video.play();
        isPlaying = true;
        icon.innerHTML = '<i class="fas fa-pause text-white text-6xl"></i>';
        icon.classList.remove('opacity-0');
        setTimeout(() => {
            icon.classList.add('opacity-0');
        }, 500);
    }
}

// 更新播放/暂停图标
function updatePlayPauseIcon() {
    const icon = document.getElementById('play-pause-icon');
    if (isPlaying) {
        icon.innerHTML = '<i class="fas fa-pause text-white text-6xl"></i>';
    } else {
        icon.innerHTML = '<i class="fas fa-play text-white text-6xl"></i>';
    }
}

// 更新进度条
function updateProgress() {
    const video = document.getElementById('main-video');
    const progressBar = document.getElementById('progress-bar');

    if (video.duration) {
        const progress = (video.currentTime / video.duration) * 100;
        progressBar.style.width = progress + '%';
    }
}

// 显示播放列表
function togglePlaylist() {
    const modal = document.getElementById('playlist-modal');
    if (modal.classList.contains('hidden')) {
        showPlaylist();
    } else {
        hidePlaylist();
    }
}

// 显示播放列表
function showPlaylist() {
    const modal = document.getElementById('playlist-modal');
    modal.classList.remove('hidden');

    // 生成播放列表
    generatePlaylist();

    // 添加显示动画
    setTimeout(() => {
        modal.querySelector('.absolute').classList.add('animate-slide-up');
    }, 10);
}

// 隐藏播放列表
function hidePlaylist() {
    const modal = document.getElementById('playlist-modal');
    modal.classList.add('hidden');
}

// 生成播放列表
function generatePlaylist() {
    const content = document.getElementById('playlist-content');

    if (videos.length === 0) {
        content.innerHTML = `
            <div class="flex items-center justify-center py-8">
                <span class="text-gray-500">暂无视频</span>
            </div>
        `;
        return;
    }

    content.innerHTML = videos.map((video, index) => `
        <div class="playlist-item flex items-center space-x-3 p-3 rounded-lg cursor-pointer ${index === currentVideo ? 'active' : ''}"
             onclick="playVideoFromList(${index})">
            <div class="video-thumbnail w-16 h-12 rounded overflow-hidden">
                <img src="${video.img}" alt="${video.title}" class="w-full h-full object-cover">
            </div>
            <div class="flex-1 min-w-0">
                <h4 class="font-medium text-sm truncate text-gray-900">${video.title}</h4>
                <p class="text-xs text-gray-500 mt-1">${video.time}</p>
            </div>
            ${index === currentVideo ? '<i class="fas fa-play text-white text-sm"></i>' : ''}
        </div>
    `).join('');
}

// 从播放列表播放视频
function playVideoFromList(index) {
    currentVideo = index;
    loadVideo(index);
    hidePlaylist();
}

// 处理滑动手势
function handleSwipe() {
    const threshold = 50; // 滑动阈值
    const diff = startY - endY;

    if (Math.abs(diff) > threshold) {
        if (diff > 0) {
            // 向上滑动 - 下一个视频
            nextVideo();
        } else {
            // 向下滑动 - 上一个视频
            previousVideo();
        }
    }
}

// 下一个视频
function nextVideo() {
    if (currentVideo < videos.length - 1) {
        currentVideo++;
        loadVideo(currentVideo);
    }
}

// 上一个视频
function previousVideo() {
    if (currentVideo > 0) {
        currentVideo--;
        loadVideo(currentVideo);
    }
}

// 加载视频
async function loadVideo(index) {
    if (index < 0 || index >= videos.length) return;

    const video = document.getElementById('main-video');
    const videoData = videos[index];

    // 更新视频信息
    document.getElementById('video-title').textContent = videoData.title;
    document.getElementById('video-time').textContent = videoData.time;

    // 先显示封面
    showVideoCover(videoData.img);

    // 获取真实视频URL
    let videoUrl = videoData.realUrl;
    if (!videoUrl && videoData.id !== 'default') {
        console.log('获取视频URL中...', videoData.id);
        videoUrl = await fetchVideoUrl(videoData.id);
        if (videoUrl) {
            // 缓存真实URL
            videos[index].realUrl = videoUrl;
        }
    }

    if (videoUrl) {
        // 隐藏封面，显示视频
        hideVideoCover();

        // 更新视频源
        video.src = videoUrl;

        // 播放新视频
        video.load();
        video.play().then(() => {
            isPlaying = true;
        }).catch(e => {
            console.error('视频播放失败:', e);
        });
    } else {
        console.error('无法获取视频URL');
    }
}

// 显示视频封面
function showVideoCover(imgUrl) {
    let coverElement = document.getElementById('video-cover');
    if (!coverElement) {
        // 创建封面元素
        coverElement = document.createElement('div');
        coverElement.id = 'video-cover';
        coverElement.className = 'absolute inset-0 z-10 flex items-center justify-center bg-black';
        coverElement.innerHTML = `
            <img src="${imgUrl}" alt="视频封面" class="w-full h-full object-cover">
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="loading-spinner"></div>
                <span class="ml-3 text-white">加载中...</span>
            </div>
        `;
        document.getElementById('video-container').appendChild(coverElement);
    } else {
        // 更新封面图片
        const img = coverElement.querySelector('img');
        if (img) {
            img.src = imgUrl;
        }
        coverElement.classList.remove('hidden');
    }
}

// 隐藏视频封面
function hideVideoCover() {
    const coverElement = document.getElementById('video-cover');
    if (coverElement) {
        coverElement.classList.add('hidden');
    }
}

// 点击模态框背景关闭播放列表
document.addEventListener('DOMContentLoaded', function() {
    const playlistModal = document.getElementById('playlist-modal');
    if (playlistModal) {
        playlistModal.addEventListener('click', function(e) {
            if (e.target === this) {
                hidePlaylist();
            }
        });
    }
});
