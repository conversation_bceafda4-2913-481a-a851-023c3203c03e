<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script >
        !function(t,n){var r,e;"object"==typeof exports&&"undefined"!=typeof module?module.exports=n():"function"==typeof define&&define.amd?define(n):(r=t.Base64,(e=n()).noConflict=function(){return t.Base64=r,e},t.Meteor&&(Base64=e),t.Base64=e)}("undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:this,(function(){"use strict";var t,n="3.7.5",r="function"==typeof atob,e="function"==typeof btoa,o="function"==typeof Buffer,u="function"==typeof TextDecoder?new TextDecoder:void 0,i="function"==typeof TextEncoder?new TextEncoder:void 0,f=Array.prototype.slice.call("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="),c=(t={},f.forEach((function(n,r){return t[n]=r})),t),a=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,d=String.fromCharCode.bind(String),s="function"==typeof Uint8Array.from?Uint8Array.from.bind(Uint8Array):function(t){return new Uint8Array(Array.prototype.slice.call(t,0))},l=function(t){return t.replace(/=/g,"").replace(/[+\/]/g,(function(t){return"+"==t?"-":"_"}))},h=function(t){return t.replace(/[^A-Za-z0-9\+\/]/g,"")},p=function(t){for(var n,r,e,o,u="",i=t.length%3,c=0;c<t.length;){if((r=t.charCodeAt(c++))>255||(e=t.charCodeAt(c++))>255||(o=t.charCodeAt(c++))>255)throw new TypeError("invalid character found");u+=f[(n=r<<16|e<<8|o)>>18&63]+f[n>>12&63]+f[n>>6&63]+f[63&n]}return i?u.slice(0,i-3)+"===".substring(i):u},y=e?function(t){return btoa(t)}:o?function(t){return Buffer.from(t,"binary").toString("base64")}:p,A=o?function(t){return Buffer.from(t).toString("base64")}:function(t){for(var n=[],r=0,e=t.length;r<e;r+=4096)n.push(d.apply(null,t.subarray(r,r+4096)));return y(n.join(""))},b=function(t,n){return void 0===n&&(n=!1),n?l(A(t)):A(t)},g=function(t){if(t.length<2)return(n=t.charCodeAt(0))<128?t:n<2048?d(192|n>>>6)+d(128|63&n):d(224|n>>>12&15)+d(128|n>>>6&63)+d(128|63&n);var n=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return d(240|n>>>18&7)+d(128|n>>>12&63)+d(128|n>>>6&63)+d(128|63&n)},B=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,x=function(t){return t.replace(B,g)},C=o?function(t){return Buffer.from(t,"utf8").toString("base64")}:i?function(t){return A(i.encode(t))}:function(t){return y(x(t))},m=function(t,n){return void 0===n&&(n=!1),n?l(C(t)):C(t)},v=function(t){return m(t,!0)},U=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,F=function(t){switch(t.length){case 4:var n=((7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3))-65536;return d(55296+(n>>>10))+d(56320+(1023&n));case 3:return d((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return d((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},w=function(t){return t.replace(U,F)},S=function(t){if(t=t.replace(/\s+/g,""),!a.test(t))throw new TypeError("malformed base64.");t+="==".slice(2-(3&t.length));for(var n,r,e,o="",u=0;u<t.length;)n=c[t.charAt(u++)]<<18|c[t.charAt(u++)]<<12|(r=c[t.charAt(u++)])<<6|(e=c[t.charAt(u++)]),o+=64===r?d(n>>16&255):64===e?d(n>>16&255,n>>8&255):d(n>>16&255,n>>8&255,255&n);return o},E=r?function(t){return atob(h(t))}:o?function(t){return Buffer.from(t,"base64").toString("binary")}:S,D=o?function(t){return s(Buffer.from(t,"base64"))}:function(t){return s(E(t).split("").map((function(t){return t.charCodeAt(0)})))},R=function(t){return D(T(t))},z=o?function(t){return Buffer.from(t,"base64").toString("utf8")}:u?function(t){return u.decode(D(t))}:function(t){return w(E(t))},T=function(t){return h(t.replace(/[-_]/g,(function(t){return"-"==t?"+":"/"})))},Z=function(t){return z(T(t))},j=function(t){return{value:t,enumerable:!1,writable:!0,configurable:!0}},I=function(){var t=function(t,n){return Object.defineProperty(String.prototype,t,j(n))};t("fromBase64",(function(){return Z(this)})),t("toBase64",(function(t){return m(this,t)})),t("toBase64URI",(function(){return m(this,!0)})),t("toBase64URL",(function(){return m(this,!0)})),t("toUint8Array",(function(){return R(this)}))},O=function(){var t=function(t,n){return Object.defineProperty(Uint8Array.prototype,t,j(n))};t("toBase64",(function(t){return b(this,t)})),t("toBase64URI",(function(){return b(this,!0)})),t("toBase64URL",(function(){return b(this,!0)}))},P={version:n,VERSION:"3.7.5",atob:E,atobPolyfill:S,btoa:y,btoaPolyfill:p,fromBase64:Z,toBase64:m,encode:m,encodeURI:v,encodeURL:v,utob:x,btou:w,decode:Z,isValid:function(t){if("string"!=typeof t)return!1;var n=t.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(n)||!/[^\s0-9a-zA-Z\-_]/.test(n)},fromUint8Array:b,toUint8Array:R,extendString:I,extendUint8Array:O,extendBuiltins:function(){I(),O()},Base64:{}};return Object.keys(P).forEach((function(t){return P.Base64[t]=P[t]})),P}));
    </script>
    <style>
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .video-item {
            transition: all 0.2s ease-in-out;
        }
        .video-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
        }
        .aspect-video {
            aspect-ratio: 16 / 9;
        }
        .hover\:bg-gray-750:hover {
            background-color: #374151;
        }
        .modal {
            backdrop-filter: blur(5px);
        }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .tab-scroll::-webkit-scrollbar {
            display: none;
        }
        .tab-scroll {
            -ms-overflow-style: none;
            scrollbar-width: none;
        }
    </style>
</head>
<body class="bg-gray-900 min-h-screen text-white">
    <!-- 头部分类选择 -->
    <div class="bg-gray-800 shadow-sm sticky top-0 z-10">
        <div class="container mx-auto px-4 py-3">
            <h1 class="text-xl font-bold text-white mb-3">视频列表</h1>
            <div class="flex overflow-x-auto space-x-2 tab-scroll" id="categoryTabs">
                <!-- 分类标签将通过JS动态生成 -->
            </div>
        </div>
    </div>

    <!-- 视频列表容器 -->
    <div class="container mx-auto px-4 py-4">
        <div id="videoList" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <!-- 视频列表将通过JS动态生成 -->
        </div>

        <!-- 加载更多按钮 -->
        <div class="text-center mt-6">
            <button id="loadMoreBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors">
                加载更多
            </button>
            <div id="loadingIndicator" class="hidden mt-4">
                <div class="inline-block w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full loading"></div>
                <span class="ml-2 text-gray-400">加载中...</span>
            </div>
        </div>
    </div>

    <!-- 视频播放弹窗 -->
    <div id="videoModal" class="fixed inset-0 bg-black bg-opacity-75 modal hidden z-50 flex items-center justify-center">
        <div class="bg-gray-800 rounded-lg p-4 max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
            <div class="flex justify-between items-center mb-4">
                <h3 id="modalTitle" class="text-lg font-bold text-white"></h3>
                <button id="closeModal" class="text-gray-400 hover:text-white text-2xl">&times;</button>
            </div>
            <div class="relative">
                <video id="videoPlayer" class="w-full h-auto max-h-[70vh]" controls>
                    您的浏览器不支持视频播放。
                </video>
            </div>
        </div>
    </div>

    <script>
        // 分类配置
        const types = [
            "next=watch",
            "category=hot",
            "category=top",
            "category=nonpaid",
            "category=long",
            "category=longer",
            "category=tf",
            "category=rf",
            "category=hd",
            "category=top&m=-1",
            "category=md",
            "category=mf",
        ];

        const typeNames = [
            "最新", "热门", "排行", "免费", "长视频", "超长", "TF", "RF", "高清", "排行-1", "MD", "MF"
        ];

        // 全局变量
        let currentType = 0;
        let currentPage = 1;
        let isLoading = false;
        let videoList = [];

        // Base64解码函数（使用base64.js库）
        function base64Decode(str) {
            try {
                return Base64.decode(str);
            } catch (error) {
                console.error('Base64解码失败:', error);
                throw new Error('Base64解码失败');
            }
        }

        // 初始化分类标签
        function initCategoryTabs() {
            const tabsContainer = document.getElementById('categoryTabs');
            typeNames.forEach((name, index) => {
                const tab = document.createElement('button');
                tab.className = `px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                    index === currentType
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`;
                tab.textContent = name;
                tab.onclick = () => switchCategory(index);
                tabsContainer.appendChild(tab);
            });
        }

        // 切换分类
        function switchCategory(typeIndex) {
            if (typeIndex === currentType || isLoading) return;

            currentType = typeIndex;
            currentPage = 1;
            videoList = [];

            // 更新标签样式
            const tabs = document.querySelectorAll('#categoryTabs button');
            tabs.forEach((tab, index) => {
                tab.className = `px-4 py-2 rounded-lg whitespace-nowrap transition-colors ${
                    index === currentType
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`;
            });

            // 清空列表并加载新数据
            document.getElementById('videoList').innerHTML = '';
            loadVideos(true);
        }

        // 加载视频数据
        async function loadVideos(isRefresh = false) {
            if (isLoading) return;

            isLoading = true;
            const loadingIndicator = document.getElementById('loadingIndicator');
            const loadMoreBtn = document.getElementById('loadMoreBtn');

            loadingIndicator.classList.remove('hidden');
            loadMoreBtn.style.display = 'none';

            try {
                const response = await fetch(`http://127.0.0.1:3000/?type=${currentType}&page=${currentPage}`);
                const result = await response.json();

                if (result.error) {
                    throw new Error('获取数据失败');
                }

                // 解码数据
                const decodedData = base64Decode(result.data);
                const videos = JSON.parse(decodedData);

                if (isRefresh) {
                    videoList = videos;
                } else {
                    videoList = [...videoList, ...videos];
                }

                renderVideoList();
                currentPage++;

            } catch (error) {
                console.error('加载视频失败:', error);
                alert('加载视频失败，请稍后重试');
            } finally {
                isLoading = false;
                loadingIndicator.classList.add('hidden');
                loadMoreBtn.style.display = 'block';
            }
        }

        // 渲染视频列表
        function renderVideoList() {
            const container = document.getElementById('videoList');
            container.innerHTML = '';

            videoList.forEach((video, index) => {
                const videoItem = document.createElement('div');
                videoItem.className = 'bg-gray-800 rounded-lg shadow-lg overflow-hidden video-item cursor-pointer hover:bg-gray-750 transition-all';
                videoItem.onclick = () => playVideo(video);

                videoItem.innerHTML = `
                    <div class="relative">
                        <div class="aspect-video relative">
                            <img src="${video.img}" alt="${video.title}"
                                 class="w-full h-full object-cover"
                                 onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjMzc0MTUxIi8+CjxwYXRoIGQ9Ik0xNDAgOTBMMTcwIDEwOEgxNDBWOTBaIiBmaWxsPSIjNkI3Mjg0Ii8+Cjwvdmc+'" />
                            <!-- 时间标签 -->
                            <div class="absolute top-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
                                ${video.time}
                            </div>
                        </div>
                        <div class="p-3">
                            <h3 class="text-sm font-medium text-white line-clamp-2 leading-tight">${video.title}</h3>
                        </div>
                    </div>
                `;

                container.appendChild(videoItem);
            });
        }

        // 播放视频
        async function playVideo(video) {
            const modal = document.getElementById('videoModal');
            const modalTitle = document.getElementById('modalTitle');
            const videoPlayer = document.getElementById('videoPlayer');

            modalTitle.textContent = video.title;
            modal.classList.remove('hidden');

            try {
                // 获取视频播放地址
                const response = await fetch(`http://127.0.0.1:3000/?id=${video.url}`);
                const result = await response.json();

                if (result.error) {
                    throw new Error('获取视频地址失败');
                }

                // 解码视频地址
                const decodedData = base64Decode(result.data);
                const videoData = JSON.parse(decodedData);

                videoPlayer.src = videoData.url;
                videoPlayer.load();

            } catch (error) {
                console.error('获取视频地址失败:', error);
                alert('获取视频地址失败，请稍后重试');
                closeVideoModal();
            }
        }

        // 关闭视频弹窗
        function closeVideoModal() {
            const modal = document.getElementById('videoModal');
            const videoPlayer = document.getElementById('videoPlayer');

            modal.classList.add('hidden');
            videoPlayer.pause();
            videoPlayer.src = '';
        }

        // 上拉刷新功能
        let startY = 0;
        let isRefreshing = false;

        document.addEventListener('touchstart', (e) => {
            startY = e.touches[0].clientY;
        });

        document.addEventListener('touchmove', (e) => {
            const currentY = e.touches[0].clientY;
            const diff = currentY - startY;

            if (diff > 100 && window.scrollY === 0 && !isRefreshing && !isLoading) {
                isRefreshing = true;
                refreshVideos();
            }
        });

        // 刷新视频列表
        async function refreshVideos() {
            currentPage = 1;
            await loadVideos(true);
            isRefreshing = false;
        }

        // 事件监听器
        document.getElementById('loadMoreBtn').addEventListener('click', () => {
            loadVideos();
        });

        document.getElementById('closeModal').addEventListener('click', closeVideoModal);

        document.getElementById('videoModal').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                closeVideoModal();
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeVideoModal();
            }
        });

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            initCategoryTabs();
            loadVideos(true);
        });
    </script>
</body>
</html>
